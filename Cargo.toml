[package]
name = "pow"
version = "0.1.0"
edition = "2021"

[dependencies]
sha3 = "0.10"
hex = "0.4"
ethers = "1.0"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
dotenv = "0.15"
# 多线程相关依赖
rayon = "1.8"
crossbeam = "0.8"
num_cpus = "1.16"
parking_lot = "0.12"

# 性能优化配置
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 2  # 开发时也启用一定优化

[[bin]]
name = "main"
path = "src/main.rs"