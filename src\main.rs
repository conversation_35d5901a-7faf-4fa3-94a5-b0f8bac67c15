use std::time::{Instant, SystemTime, UNIX_EPOCH};
use std::env;
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::sync::Arc;
use std::thread;
use ethers::{
    prelude::*,
    providers::{Http, Provider},
    signers::{LocalWallet, Signer},
    types::{TransactionRequest, U256, Address, transaction::eip2718::TypedTransaction},
    middleware::SignerMiddleware,
    utils::keccak256,
};
use crossbeam::channel;

// 搜索结果结构体
#[derive(Debug, Clone)]
struct SearchResult {
    nonce: u64,
    hash: String,
    data: String,
    attempts: u64,
    thread_id: usize,
}

// 多线程共享状态
struct SharedState {
    found: AtomicBool,
    total_attempts: AtomicU64,
    start_time: Instant,
}

impl SharedState {
    fn new() -> Self {
        Self {
            found: AtomicBool::new(false),
            total_attempts: AtomicU64::new(0),
            start_time: Instant::now(),
        }
    }
    
    fn is_found(&self) -> bool {
        self.found.load(Ordering::Relaxed)
    }
    
    fn mark_found(&self) {
        self.found.store(true, Ordering::Relaxed);
    }
    
    fn add_attempts(&self, count: u64) {
        self.total_attempts.fetch_add(count, Ordering::Relaxed);
    }
    
    fn get_total_attempts(&self) -> u64 {
        self.total_attempts.load(Ordering::Relaxed)
    }
}

// 配置结构体
#[derive(Debug, Clone)]
struct Config {
    private_key: String,
    rpc_url: String,
    to_address: String,
    chain_id: u64,
    gas_price: U256,
    gas_limit: U256,
}

impl Config {
    fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        // 加载 .env 文件
        dotenv::dotenv().ok();
        
        Ok(Self {
            private_key: env::var("PRIVATE_KEY")
                .unwrap_or_else(|_| "7a4d009b4ab1201a0b5142c02f94babf76dbeb18198437597f2ee632e3078d3d".to_string()),
            rpc_url: env::var("RPC_URL")
                .unwrap_or_else(|_| "https://eth-sepolia.g.alchemy.com/v2/********************************".to_string()),
            to_address: env::var("TO_ADDRESS")
                .unwrap_or_else(|_| "******************************************".to_string()),
            chain_id: env::var("CHAIN_ID")
                .unwrap_or_else(|_| "11155111".to_string())
                .parse()
                .unwrap_or(11155111),
            gas_price: U256::from(
                env::var("GAS_PRICE")
                    .unwrap_or_else(|_| "1000000000".to_string())
                    .parse::<u64>()
                    .unwrap_or(1_000_000_000u64)
            ),
            gas_limit: U256::from(
                env::var("GAS_LIMIT")
                    .unwrap_or_else(|_| "50000".to_string())
                    .parse::<u64>()
                    .unwrap_or(50000u64)
            ),
        })
    }
}

// 交易搜索和上链工具
#[derive(Clone)]
struct HashSearcher {
    data_template: String,
    config: Config,
    wallet: LocalWallet,
    current_nonce: U256,
    num_threads: usize,
    // 预解析的地址以避免重复解析
    to_address: Address,
}

impl HashSearcher {
    async fn new(config: Config) -> Result<Self, Box<dyn std::error::Error>> {
        let data_template = r#"data:application/json,{{"p":"ierc-20","op":"mint","tick":"ore","amt":"1","nonce":"{}"}}"#;
        
        // 创建钱包
        let wallet: LocalWallet = config.private_key.parse()?;
        let wallet = wallet.with_chain_id(config.chain_id);
        
        // 创建provider获取当前nonce
        let provider = Provider::<Http>::try_from(&config.rpc_url)?;
        let client = SignerMiddleware::new(provider.clone(), wallet.clone());
        let current_nonce = client.get_transaction_count(wallet.address(), None).await?;
        
        println!("📍 钱包地址: {:?}", wallet.address());
        println!("🌐 链ID: {}", config.chain_id);
        println!("📊 当前nonce: {}", current_nonce);
        
        // 检查余额
        let balance = client.get_balance(wallet.address(), None).await?;
        println!("💰 当前余额: {} ETH", ethers::utils::format_ether(balance));
        
        if balance.is_zero() {
            return Err("余额不足，请先充值！".into());
        }
        
        // 获取CPU核心数，但限制最大线程数以防止内存溢出
        let num_threads = std::cmp::min(num_cpus::get(), 8);
        
        // 预解析目标地址
        let to_address = config.to_address.parse::<Address>()?;
        
        Ok(HashSearcher {
            data_template: data_template.to_string(),
            config,
            wallet,
            current_nonce,
            num_threads,
            to_address,
        })
    }
    
    // 极速哈希计算（最小化内存分配）
    fn calculate_transaction_hash_fast(&self, search_nonce: u64, rt: &tokio::runtime::Runtime) -> Result<String, Box<dyn std::error::Error>> {
        // 使用格式化避免字符串替换
        let data_string = format!(
            r#"data:application/json,{{"p":"ierc-20","op":"mint","tick":"ore","amt":"1","nonce":"{}"}}"#,
            search_nonce
        );

        // 创建交易（使用预解析的地址）
        let tx = TransactionRequest::new()
            .to(self.to_address)
            .value(U256::zero())
            .data(data_string.into_bytes())
            .gas_price(self.config.gas_price)
            .gas(self.config.gas_limit)
            .nonce(self.current_nonce)
            .chain_id(self.config.chain_id);

        // 转换为TypedTransaction
        let typed_tx: TypedTransaction = tx.into();

        // 使用共享运行时来签名交易
        let signature = rt.block_on(self.wallet.sign_transaction(&typed_tx))?;

        // 编码已签名的交易
        let signed_tx = typed_tx.rlp_signed(&signature);

        // 计算交易hash
        let tx_hash = keccak256(&signed_tx);

        // 直接使用hex::encode避免额外分配
        Ok(format!("0x{}", hex::encode(tx_hash)))
    }
    
    // 计算真实的交易hash（备用方法）
    #[allow(dead_code)]
    async fn calculate_real_transaction_hash(&self, search_nonce: u64) -> Result<String, Box<dyn std::error::Error>> {
        // 构建数据字符串
        let data_string = self.data_template.replace("{}", &search_nonce.to_string());
        let data_bytes = data_string.clone().into_bytes();
        
        // 创建交易
        let tx = TransactionRequest::new()
            .to(self.config.to_address.parse::<Address>()?)
            .value(U256::zero())
            .data(data_bytes)
            .gas_price(self.config.gas_price)
            .gas(self.config.gas_limit)
            .nonce(self.current_nonce)
            .chain_id(self.config.chain_id);
        
        // 转换为TypedTransaction
        let typed_tx: TypedTransaction = tx.into();
        
        // 签名交易
        let signature = self.wallet.sign_transaction(&typed_tx).await?;
        
        // 编码已签名的交易
        let signed_tx = typed_tx.rlp_signed(&signature);
        
        // 计算交易hash
        let tx_hash = keccak256(&signed_tx);
        let hash_hex = hex::encode(tx_hash);
        
        Ok(hash_hex)
    }
    
    // 生成交易数据
    fn generate_transaction_data(&self, search_nonce: u64) -> String {
        self.data_template.replace("{}", &search_nonce.to_string())
    }
    
    // 发送交易到区块链（完全复制搜索时的逻辑）
    async fn send_transaction(&self, search_nonce: u64) -> Result<String, Box<dyn std::error::Error>> {
        println!("🚀 发送交易到区块链...");

        // 使用与搜索完全相同的逻辑构建交易
        let data_string = format!(
            r#"data:application/json,{{"p":"ierc-20","op":"mint","tick":"ore","amt":"1","nonce":"{}"}}"#,
            search_nonce
        );

        // 创建provider
        let provider = Provider::<Http>::try_from(&self.config.rpc_url)?;
        let client = SignerMiddleware::new(provider.clone(), self.wallet.clone());

        // 验证当前nonce是否仍然有效
        let current_nonce = client.get_transaction_count(self.wallet.address(), None).await?;

        if current_nonce != self.current_nonce {
            return Err(format!("Nonce已变化！搜索时nonce: {}, 当前nonce: {}", self.current_nonce, current_nonce).into());
        }

        // 创建交易（与搜索时完全相同的参数）
        let tx = TransactionRequest::new()
            .to(self.to_address)
            .value(U256::zero())
            .data(data_string.clone().into_bytes())
            .gas_price(self.config.gas_price)
            .gas(self.config.gas_limit)
            .nonce(self.current_nonce)
            .chain_id(self.config.chain_id);

        println!("📦 交易详情:");
        println!("  - To: {}", self.config.to_address);
        println!("  - Data: {}", data_string);
        println!("  - Gas Limit: {}", self.config.gas_limit);
        println!("  - Gas Price: {} gwei", self.config.gas_price / 1_000_000_000u64);
        println!("  - 区块链Nonce: {} (交易字段)", self.current_nonce);
        println!("  - 搜索Nonce: {} (数据字段)", search_nonce);

        // 转换为TypedTransaction并签名（与搜索时相同）
        let typed_tx: TypedTransaction = tx.into();
        let signature = self.wallet.sign_transaction(&typed_tx).await?;
        let signed_tx = typed_tx.rlp_signed(&signature);

        // 计算预期的交易hash（应该与搜索结果匹配）
        let expected_hash = format!("0x{}", hex::encode(keccak256(&signed_tx)));
        println!("🔮 预期交易Hash: {}", expected_hash);

        // 发送已签名的交易
        let pending_tx = provider.send_raw_transaction(signed_tx.clone()).await?;
        let actual_hash = format!("0x{:x}", pending_tx.tx_hash());

        println!("✅ 交易已发送！");
        println!("🔗 实际交易Hash: {}", actual_hash);
        println!("⏳ 等待确认中...");

        // 等待交易确认
        let receipt = provider.get_transaction_receipt(pending_tx.tx_hash()).await?;
        match receipt {
            Some(receipt) => {
                println!("🎯 交易已确认！");
                println!("📦 区块号: {:?}", receipt.block_number);
                println!("⛽ Gas使用量: {:?}", receipt.gas_used);
                println!("✅ 交易状态: {:?}", receipt.status);

                if receipt.status == Some(U64::from(1)) {
                    println!("🎉 交易执行成功！");
                } else {
                    println!("❌ 交易执行失败！");
                }
            }
            None => {
                println!("⚠️ 交易已发送但无法获取收据");
            }
        }

        Ok(actual_hash)
    }
    
    // 多线程工作函数
    fn worker_search(
        &self,
        thread_id: usize,
        start_nonce: u64,
        target_zeros: usize,
        shared_state: Arc<SharedState>,
        result_sender: channel::Sender<SearchResult>,
        progress_interval: u64,
    ) {
        // 为每个工作线程创建一个运行时（避免重复创建）
        let rt = match tokio::runtime::Runtime::new() {
            Ok(rt) => rt,
            Err(e) => {
                eprintln!("线程 {} 创建运行时失败: {}", thread_id, e);
                return;
            }
        };
        
        let mut search_nonce = start_nonce + thread_id as u64;
        let mut local_attempts = 0u64;
        let target_prefix = "0".repeat(target_zeros);
        let step = self.num_threads as u64;
        
        // 内存优化：定期清理和限制变量作用域
        const MEMORY_CLEANUP_INTERVAL: u64 = 50000;
        
        loop {
            // 检查是否已找到结果
            if shared_state.is_found() {
                break;
            }
            
            local_attempts += 1;
            
            // 内存优化：限制变量作用域
            let hash_result = {
                match self.calculate_transaction_hash_fast(search_nonce, &rt) {
                    Ok(hash) => hash,
                    Err(e) => {
                        if local_attempts % 10000 == 0 {
                            eprintln!("线程 {} 计算哈希时出错: {}", thread_id, e);
                        }
                        search_nonce += step;
                        continue;
                    }
                }
            };
            
            // 检查是否找到目标哈希
            if hash_result.starts_with(&target_prefix) {
                shared_state.mark_found();
                
                let data = self.generate_transaction_data(search_nonce);
                let result = SearchResult {
                    nonce: search_nonce,
                    hash: hash_result,
                    data,
                    attempts: local_attempts,
                    thread_id,
                };
                
                // 发送结果
                if result_sender.send(result).is_err() {
                    eprintln!("线程 {} 发送结果失败", thread_id);
                }
                break;
            }
            
            // 定期更新全局统计和内存清理
            if local_attempts % progress_interval == 0 {
                shared_state.add_attempts(progress_interval);
                
                // 内存清理
                if local_attempts % MEMORY_CLEANUP_INTERVAL == 0 {
                    // 强制垃圾回收（在Rust中主要是确保变量被正确释放）
                    std::hint::black_box(());
                }
            }
            
            search_nonce += step;
            
            // 防止nonce溢出
            if search_nonce >= u64::MAX - step {
                search_nonce = thread_id as u64;
            }
            
            // 防止单线程无限运行
            if local_attempts > 100_000_000 {
                println!("线程 {} 达到最大尝试次数，停止搜索", thread_id);
                break;
            }
        }
        
        // 最终更新统计
        shared_state.add_attempts(local_attempts % progress_interval);
    }
    
    // 多线程搜索并直接上链
    async fn multithreaded_search_and_send(&self, start_nonce: u64, target_zeros: usize) -> Result<(), Box<dyn std::error::Error>> {
        println!("🚀 启动多线程搜索模式");
        println!("🧵 使用线程数: {}", self.num_threads);
        println!("🎯 目标: {}个零开头的交易hash", target_zeros);
        println!("📍 起始nonce: {}", start_nonce);
        println!("⚡ 优化: 预解析地址 + 快速格式化 + 共享运行时");
        
        // 获取当前最新的nonce用于搜索
        let provider = Provider::<Http>::try_from(&self.config.rpc_url)?;
        let client = SignerMiddleware::new(provider.clone(), self.wallet.clone());
        let search_nonce_base = client.get_transaction_count(self.wallet.address(), None).await?;
        
        println!("🔢 使用实时nonce进行搜索: {}", search_nonce_base);
        println!("💡 这确保搜索到的hash与实际发送的交易hash完全一致");
        
        let shared_state = Arc::new(SharedState::new());
        let (result_sender, result_receiver) = channel::unbounded();
        
        // 创建进度监控线程
        let progress_state = Arc::clone(&shared_state);
        let progress_handle = thread::spawn(move || {
            loop {
                thread::sleep(std::time::Duration::from_secs(3));
                
                if progress_state.is_found() {
                    break;
                }
                
                let total_attempts = progress_state.get_total_attempts();
                let elapsed = progress_state.start_time.elapsed();
                let speed = if elapsed.as_secs_f64() > 0.0 {
                    total_attempts as f64 / elapsed.as_secs_f64()
                } else {
                    0.0
                };
                
                println!("📈 总进度: {} 次尝试, 速度: {:.0} hash/秒, 运行时间: {:.1}s", 
                    total_attempts, speed, elapsed.as_secs_f64());
            }
        });
        
        // 创建使用实时nonce的搜索器
        let mut real_time_searcher = self.clone();
        real_time_searcher.current_nonce = search_nonce_base;
        
        // 启动工作线程
        let mut handles = Vec::with_capacity(self.num_threads);
        for thread_id in 0..self.num_threads {
            let searcher_clone = real_time_searcher.clone();
            let state_clone = Arc::clone(&shared_state);
            let sender_clone = result_sender.clone();
            
            let handle = thread::spawn(move || {
                searcher_clone.worker_search(
                    thread_id,
                    start_nonce,
                    target_zeros,
                    state_clone,
                    sender_clone,
                    10000, // 进度报告间隔（减少原子操作频率）
                );
            });
            handles.push(handle);
        }
        
        // 等待结果
        let result = match result_receiver.recv() {
            Ok(result) => result,
            Err(_) => {
                return Err("所有线程都已结束，未找到结果".into());
            }
        };
        
        // 标记找到结果，停止其他线程
        shared_state.mark_found();
        
        // 等待所有线程结束
        for handle in handles {
            let _ = handle.join();
        }
        
        // 等待进度监控线程结束
        let _ = progress_handle.join();
        
        let total_attempts = shared_state.get_total_attempts() + result.attempts;
        let elapsed = shared_state.start_time.elapsed();
        
        println!("\n🎉 找到符合条件的交易hash！");
        println!("🧵 发现线程: {}", result.thread_id);
        println!("🔢 搜索Nonce: {}", result.nonce);
        println!("🔐 真实交易Hash: 0x{}", result.hash);
        println!("📊 总尝试次数: {}", total_attempts);
        println!("⏱️  总耗时: {:.2?}", elapsed);
        println!("🚀 平均速度: {:.2} hash/秒", total_attempts as f64 / elapsed.as_secs_f64());
        
        // 生成交易数据
        println!("\n📄 交易信息:");
        println!("  - 搜索Nonce: {}", result.nonce);
        println!("  - 交易Data: {}", result.data);
        println!("  - Data Hex: 0x{}", hex::encode(result.data.as_bytes()));
        
        // 立即发送交易
        println!("\n🚀 立即发送交易到区块链...");
        match self.send_transaction(result.nonce).await {
            Ok(tx_hash) => {
                println!("\n🎉 交易发送成功！");
                println!("🔗 交易Hash: {}", tx_hash);
                println!("🌐 可以在区块浏览器中查看:");
                println!("https://sepolia.etherscan.io/tx/{}", tx_hash.trim_start_matches("0x"));
                
                // 验证交易哈希
                println!("\n🔍 验证交易哈希...");
                let clean_tx_hash = tx_hash.trim_start_matches("0x");
                if clean_tx_hash == result.hash {
                    println!("✅ 交易hash完全匹配！");
                    println!("🎯 搜索到的hash: 0x{}", result.hash);
                    println!("🎯 实际交易hash: {}", tx_hash);
                    println!("🎉 使用相同nonce确保了hash的完全一致性！");
                } else {
                    println!("❌ 交易hash不匹配（这不应该发生）:");
                    println!("  搜索到的hash: 0x{}", result.hash);
                    println!("  实际交易hash: {}", tx_hash);
                    println!("🔧 可能的原因: 网络延迟导致nonce变化或其他参数不一致");
                }
            }
            Err(e) => {
                println!("❌ 交易发送失败: {}", e);
            }
        }
        
        Ok(())
    }
    
    // 搜索并直接上链（单线程备用方法）
    #[allow(dead_code)]
    async fn search_and_send(&self, start_nonce: u64, target_zeros: usize) -> Result<(), Box<dyn std::error::Error>> {
        let mut search_nonce = start_nonce;
        let mut attempts = 0u64;
        let start_time = Instant::now();
        
        let target_prefix = "0".repeat(target_zeros);
        
        println!("🔍 开始搜索，目标: {}个零开头的交易hash", target_zeros);
        println!("📍 起始nonce: {}", start_nonce);
        println!("⚡ 找到结果后将立即上链！");
        
        loop {
            attempts += 1;
            
            // 计算真实的交易hash
            let hash_hex = self.calculate_real_transaction_hash(search_nonce).await?;
            
            if hash_hex.starts_with(&target_prefix) {
                let elapsed = start_time.elapsed();
                println!("\n🎉 找到符合条件的交易hash！");
                println!("🔢 搜索Nonce: {}", search_nonce);
                println!("🔐 真实交易Hash: 0x{}", hash_hex);
                println!("📊 尝试次数: {}", attempts);
                println!("⏱️  耗时: {:.2?}", elapsed);
                println!("🚀 平均速度: {:.2} hash/秒", attempts as f64 / elapsed.as_secs_f64());
                
                // 生成交易数据
                let transaction_data = self.generate_transaction_data(search_nonce);
                println!("\n📄 交易信息:");
                println!("  - 搜索Nonce: {}", search_nonce);
                println!("  - 交易Data: {}", transaction_data);
                println!("  - Data Hex: 0x{}", hex::encode(transaction_data.as_bytes()));
                
                // 立即发送交易
                println!("\n🚀 立即发送交易到区块链...");
                match self.send_transaction(search_nonce).await {
                    Ok(tx_hash) => {
                        println!("\n🎉 交易发送成功！");
                        println!("🔗 交易Hash: {}", tx_hash);
                        println!("🌐 可以在区块浏览器中查看:");
                        println!("https://sepolia.etherscan.io/tx/{}", tx_hash.trim_start_matches("0x"));
                        
                        // 验证交易hash是否匹配
                        let clean_tx_hash = tx_hash.trim_start_matches("0x");
                        if clean_tx_hash == hash_hex {
                            println!("✅ 交易hash完全匹配！");
                        } else {
                            println!("⚠️ 交易hash不匹配:");
                            println!("  预期: 0x{}", hash_hex);
                            println!("  实际: {}", tx_hash);
                        }
                    }
                    Err(e) => {
                        println!("❌ 交易发送失败: {}", e);
                    }
                }
                
                return Ok(());
            }
            
            // 定期显示进度（在同一行更新）
            if attempts % 1000 == 0 {
                let elapsed = start_time.elapsed();
                let speed = attempts as f64 / elapsed.as_secs_f64();
                print!("\r📈 进度: {} 次尝试, 速度: {:.2} hash/秒, 当前hash: 0x{}..., nonce: {}        ", 
                    attempts, speed, &hash_hex[..12], search_nonce);
                use std::io::{self, Write};
                io::stdout().flush().unwrap();
            }
            
            search_nonce += 1;
            
            // 防止溢出
            if search_nonce == u64::MAX {
                println!("⚠️  nonce达到最大值，重新从0开始");
                search_nonce = 0;
            }
            
            // 防止无限循环
            if attempts > 500_000_000 {
                println!("⏰ 达到最大尝试次数限制");
                break;
            }
        }
        
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 EVM交易Hash碰撞工具 + 自动上链");
    println!("🎯 目标: 找到前缀6个0的交易hash并自动发送到区块链");
    println!("⚡ 使用固定gas设置，只有data会变化");
    
    let config = Config::from_env()?;
    
    println!("\n🔧 配置信息 (从.env文件加载):");
    println!("  - RPC: {}", config.rpc_url);
    println!("  - 目标地址: {}", config.to_address);
    println!("  - Gas Price: {} gwei", config.gas_price / 1_000_000_000u64);
    println!("  - Gas Limit: {}", config.gas_limit);
    println!("  - 链ID: {}", config.chain_id);
    
    let searcher = HashSearcher::new(config).await?;
    
    // 使用当前时间戳作为起始nonce
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let start_nonce = env::var("START_NONCE")
        .map(|s| s.parse::<u64>().unwrap_or(timestamp))
        .unwrap_or(timestamp);
    
    println!("⏰ 使用时间戳作为起始nonce: {}", start_nonce);
    let target_zeros = env::var("TARGET_ZEROS")
        .unwrap_or_else(|_| "4".to_string())
        .parse::<usize>()
        .unwrap_or(4);
    
    println!("\n📊 理论上需要尝试约 {} 次", 16u64.pow(target_zeros as u32));
    println!("🎯 开始多线程搜索并自动上链...");
    
    // 使用多线程搜索
    searcher.multithreaded_search_and_send(start_nonce, target_zeros).await?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_transaction_hash_calculation() {
        let config = Config::default();
        let searcher = HashSearcher::new(config).await.unwrap();
        
        // 测试交易hash计算
        let test_nonce = 12345u64;
        let hash = searcher.calculate_real_transaction_hash(test_nonce).await.unwrap();
        
        println!("测试结果: nonce={}, hash=0x{}", test_nonce, hash);
        assert_eq!(hash.len(), 64); // 32字节 = 64个hex字符
    }
    
    #[test]
    fn test_transaction_data() {
        let config = Config::default();
        let data_template = r#"data:application/json,{"p":"ierc-20","op":"mint","tick":"ore","amt":"1","nonce":"{}"}}"#;
        
        let nonce = 14084835u64;
        let data = data_template.replace("{}", &nonce.to_string());
        let expected = r#"data:application/json,{"p":"ierc-20","op":"mint","tick":"ore","amt":"1","nonce":"14084835"}"#;
        
        assert_eq!(data, expected);
        println!("交易数据: {}", data);
    }
}